# 数字拼图游戏详细设计文档 (Detailed Design Document)

## 1. 文档概述

### 1.1 文档信息
- **项目名称**: 数字拼图游戏（15-Puzzle Game）
- **文档类型**: 详细设计文档
- **文档版本**: v1.0
- **创建日期**: 2025-08-12
- **基于需求**: 数字拼图游戏需求文档 v1.0

### 1.2 设计目标
基于需求文档，实现符合MVC模式的数字拼图游戏系统，确保所有功能需求和非功能需求得到满足。

## 2. 系统架构设计

### 2.1 整体架构设计 (NFR-004: MVC模式)

```
┌─────────────────────────────────────────────────────────────┐
│                        数字拼图游戏系统                          │
├─────────────────────────────────────────────────────────────┤
│  Main Class (主程序模块)                                      │
│  - 程序入口点                                                │
│  - 模块协调                                                  │
├─────────────────────────────────────────────────────────────┤
│                         MVC架构                             │
├───────────────┬─────────────────┬───────────────────────────┤
│     View      │   Controller    │          Model            │
│   (视图层)     │    (控制层)      │         (模型层)           │
├───────────────┼─────────────────┼───────────────────────────┤
│ PuzzleGameUI  │  PuzzleGame     │       GameBoard           │
│ - Swing界面   │  - 游戏逻辑控制  │  - 4×4网格数据管理         │
│ - 用户交互    │  - 状态管理     │  - 数字方块状态            │
│ - 界面更新    │  - 移动验证     │  - 位置数据               │
│ - 事件处理    │  - 胜利判断     │  - 游戏状态数据            │
└───────────────┴─────────────────┴───────────────────────────┘
```

### 2.2 模块职责和接口

#### 2.2.1 Model层 - GameBoard类
**职责**: 
- 管理4×4数字网格数据 (FR-001)
- 存储数字方块状态 (FR-002)
- 提供数据访问接口

**对外接口**:
```java
// 数据获取接口
int[][] getBoard()
Point getEmptyPosition()
boolean isValidMove(int row, int col)

// 数据修改接口
void moveBlock(int row, int col)
void shuffleBoard()
boolean isWinState()
```

#### 2.2.2 Controller层 - PuzzleGame类
**职责**:
- 游戏逻辑控制
- 移动验证 (FR-006, FR-007)
- 胜利判断 (FR-010)
- 游戏状态管理

**对外接口**:
```java
// 游戏控制接口
boolean attemptMove(int row, int col)
void resetGame()
GameState getGameState()

// 事件通知接口
void addGameStateListener(GameStateListener listener)
void notifyGameStateChanged()
```

#### 2.2.3 View层 - PuzzleGameUI类
**职责**:
- Swing界面实现 (NFR-002)
- 用户交互处理 (FR-005)
- 界面更新和反馈 (NFR-009)

**对外接口**:
```java
// 界面控制接口
void updateDisplay()
void showWinMessage()
void enableResetButton()

// 事件处理接口
void onBlockClick(int row, int col)
void onResetClick()
```

## 3. 详细类图设计

### 3.1 核心类设计 (基于需求4.2节)

```mermaid
classDiagram
    class Main {
        +main(String[] args) void
        -initializeGame() void
    }
    
    class GameBoard {
        -int[][] board
        -Point emptyPosition
        -int size
        
        +GameBoard()
        +getBoard() int[][]
        +getEmptyPosition() Point
        +isValidMove(int row, int col) boolean
        +moveBlock(int row, int col) void
        +shuffleBoard() void
        +isWinState() boolean
        -isAdjacent(int row, int col) boolean
        -swapWithEmpty(int row, int col) void
    }
    
    class PuzzleGame {
        -GameBoard gameBoard
        -GameState currentState
        -List~GameStateListener~ listeners
        
        +PuzzleGame()
        +attemptMove(int row, int col) boolean
        +resetGame() void
        +getGameState() GameState
        +getBoard() int[][]
        +addGameStateListener(GameStateListener listener) void
        -notifyGameStateChanged() void
        -checkWinCondition() void
    }
    
    class PuzzleGameUI {
        -PuzzleGame game
        -JFrame frame
        -JButton[][] buttons
        -JButton resetButton
        -JLabel statusLabel
        
        +PuzzleGameUI(PuzzleGame game)
        +show() void
        +updateDisplay() void
        +showWinMessage() void
        -createGameBoard() void
        -createControlPanel() void
        -onBlockClick(int row, int col) void
        -onResetClick() void
        -setupEventHandlers() void
    }
    
    class GameState {
        <<enumeration>>
        PLAYING
        WON
        RESET
    }
    
    class GameStateListener {
        <<interface>>
        +onGameStateChanged(GameState newState) void
    }
    
    Main --> PuzzleGame
    Main --> PuzzleGameUI
    PuzzleGame --> GameBoard
    PuzzleGame --> GameState
    PuzzleGame --> GameStateListener
    PuzzleGameUI --> PuzzleGame
    PuzzleGameUI ..|> GameStateListener
```

### 3.2 类详细说明

#### 3.2.1 GameBoard类 (FR-001, FR-002)
```java
public class GameBoard {
    private int[][] board;           // 4×4数字网格
    private Point emptyPosition;     // 空白格位置
    private static final int SIZE = 4;
    
    // 构造函数 - 初始化并打乱游戏板
    public GameBoard() {
        initializeBoard();
        shuffleBoard();
    }
    
    // 验证移动是否有效 (FR-006, FR-007)
    public boolean isValidMove(int row, int col) {
        return isAdjacent(row, col) && !isEmptyPosition(row, col);
    }
    
    // 胜利状态判断 (FR-010)
    public boolean isWinState() {
        // 检查数字1-15是否按顺序排列，空格在右下角
    }
}
```

#### 3.2.2 PuzzleGame类 
```java
public class PuzzleGame {
    private GameBoard gameBoard;
    private GameState currentState;
    
    // 尝试移动方块 (FR-005, FR-008)
    public boolean attemptMove(int row, int col) {
        if (!gameBoard.isValidMove(row, col)) {
            return false; // 无反应处理
        }
        
        gameBoard.moveBlock(row, col);
        checkWinCondition();
        notifyGameStateChanged();
        return true;
    }
    
    // 胜利条件检查 (FR-010, FR-011)
    private void checkWinCondition() {
        if (gameBoard.isWinState()) {
            currentState = GameState.WON;
        }
    }
}
```

#### 3.2.3 PuzzleGameUI类 (NFR-002)
```java
public class PuzzleGameUI implements GameStateListener {
    private JButton[][] buttons;     // 4×4按钮网格 (FR-003)
    private JButton resetButton;     // 重置按钮 (FR-013)
    private JLabel statusLabel;      // 状态显示 (FR-014)
    
    // 界面布局 (FR-015)
    private void createGameBoard() {
        // 创建4×4按钮网格布局
        // 数字方块清晰显示 (FR-003)
        // 空白格视觉区分 (FR-004)
    }
    
    // 游戏状态变化响应
    @Override
    public void onGameStateChanged(GameState newState) {
        updateDisplay();
        if (newState == GameState.WON) {
            showWinMessage(); // FR-011
        }
    }
}
```

## 4. 状态图设计

### 4.1 游戏状态转换图 (FR-010)

```mermaid
stateDiagram-v2
    [*] --> 初始化
    初始化 --> 游戏进行中 : 游戏开始/数字打乱(FR-009)
    
    游戏进行中 --> 游戏进行中 : 有效移动(FR-005,FR-006)/更新界面
    游戏进行中 --> 游戏进行中 : 无效点击(FR-007,FR-008)/无反应
    游戏进行中 --> 胜利状态 : 胜利条件满足(FR-010)/显示胜利信息(FR-011)
    
    胜利状态 --> 游戏进行中 : 重置游戏(FR-012,FR-013)/重新打乱
    游戏进行中 --> 游戏进行中 : 重置游戏(FR-012,FR-013)/重新打乱
    
    state 游戏进行中 {
        [*] --> 等待用户输入
        等待用户输入 --> 验证移动 : 用户点击方块
        验证移动 --> 执行移动 : 移动有效
        验证移动 --> 等待用户输入 : 移动无效
        执行移动 --> 检查胜利条件 : 移动完成
        检查胜利条件 --> 等待用户输入 : 未胜利
        检查胜利条件 --> [*] : 胜利
    }
    
    state 胜利状态 {
        [*] --> 显示胜利信息
        显示胜利信息 --> 等待重置 : 胜利提示完成
        等待重置 --> [*] : 用户点击重置
    }
```

### 4.2 状态说明

**状态定义**:
- **初始化**: 游戏启动，初始化4×4网格
- **游戏进行中**: 用户可以进行移动操作
- **胜利状态**: 达成胜利条件，显示胜利信息

**转换条件**:
- **有效移动**: 点击与空格相邻的数字方块 (FR-006)
- **无效移动**: 点击不可移动的方块 (FR-007, FR-008)
- **胜利条件**: 数字1-15按顺序排列，空格在右下角 (FR-010)

## 5. 时序图设计

### 5.1 用户点击移动流程时序图 (FR-005)

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as PuzzleGameUI
    participant Game as PuzzleGame
    participant Board as GameBoard
    
    Note over User,Board: FR-005: 用户点击移动完整流程
    
    User->>UI: 点击数字方块
    Note right of User: 用户操作触发
    
    UI->>UI: onBlockClick(row, col)
    Note right of UI: 界面事件处理
    
    UI->>Game: attemptMove(row, col)
    Note right of UI: 传递移动请求
    
    Game->>Board: isValidMove(row, col)
    Note right of Game: 验证移动有效性(FR-006,FR-007)
    
    alt 移动有效
        Board-->>Game: true
        Game->>Board: moveBlock(row, col)
        Note right of Game: 执行移动操作
        
        Board->>Board: swapWithEmpty(row, col)
        Note right of Board: 交换方块位置
        
        Board-->>Game: 移动完成
        
        Game->>Board: isWinState()
        Note right of Game: 检查胜利条件(FR-010)
        
        alt 达成胜利
            Board-->>Game: true
            Game->>Game: currentState = WON
            Game->>UI: notifyGameStateChanged()
            Note right of Game: 通知状态变化
            
            UI->>UI: showWinMessage()
            Note right of UI: 显示胜利信息(FR-011)
        else 未达成胜利
            Board-->>Game: false
            Game->>UI: notifyGameStateChanged()
        end
        
        Game-->>UI: true
        UI->>UI: updateDisplay()
        Note right of UI: 更新界面显示
        
    else 移动无效
        Board-->>Game: false
        Game-->>UI: false
        Note right of UI: 无反应处理(FR-008)
    end
    
    Note over User,Board: 响应时间要求: <100ms (NFR-005)
```

### 5.2 重置游戏流程时序图 (FR-012, FR-013)

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as PuzzleGameUI
    participant Game as PuzzleGame
    participant Board as GameBoard
    
    Note over User,Board: FR-012,FR-013: 重置游戏流程
    
    User->>UI: 点击重置按钮
    UI->>UI: onResetClick()
    UI->>Game: resetGame()
    
    Game->>Board: shuffleBoard()
    Note right of Game: 重新打乱数字方块(FR-009)
    
    Board->>Board: 随机打乱网格
    Board-->>Game: 打乱完成
    
    Game->>Game: currentState = PLAYING
    Game->>UI: notifyGameStateChanged()
    
    UI->>UI: updateDisplay()
    Note right of UI: 更新界面显示
    
    Note over User,Board: 响应时间要求: <200ms (NFR-007)
```

## 6. 界面设计原型

### 6.1 主界面布局设计 (FR-001, FR-003, FR-013, FR-015)

```
┌─────────────────────────────────────────────────────┐
│                数字拼图游戏                           │
├─────────────────────────────────────────────────────┤
│  状态: 游戏进行中                     [重置游戏]     │  ← FR-013, FR-014
├─────────────────────────────────────────────────────┤
│                                                     │
│    ┌────┐ ┌────┐ ┌────┐ ┌────┐                     │
│    │ 1  │ │ 2  │ │ 3  │ │ 4  │                     │  ← FR-001: 4×4网格
│    └────┘ └────┘ └────┘ └────┘                     │  ← FR-003: 清晰显示数字
│                                                     │
│    ┌────┐ ┌────┐ ┌────┐ ┌────┐                     │
│    │ 5  │ │ 6  │ │ 7  │ │ 8  │                     │
│    └────┘ └────┘ └────┘ └────┘                     │
│                                                     │
│    ┌────┐ ┌────┐ ┌────┐ ┌────┐                     │
│    │ 9  │ │ 10 │ │ 11 │ │ 12 │                     │
│    └────┘ └────┘ └────┘ └────┘                     │
│                                                     │
│    ┌────┐ ┌────┐ ┌────┐ ┌────┐                     │
│    │ 13 │ │ 14 │ │ 15 │ │    │                     │  ← FR-004: 空白格视觉区分
│    └────┘ └────┘ └────┘ └────┘                     │
│                                                     │
└─────────────────────────────────────────────────────┘
```

### 6.2 界面元素规格

#### 6.2.1 数字方块设计 (FR-003, FR-004)
- **尺寸**: 60×60像素
- **字体**: Arial, 18pt, 粗体
- **颜色**: 
  - 数字方块: 浅蓝色背景 (#E6F3FF)
  - 空白格: 灰色背景 (#F0F0F0)
  - 边框: 深灰色 (#808080)
- **间距**: 方块间距5像素

#### 6.2.2 控制按钮设计 (FR-013)
- **重置按钮**: 
  - 尺寸: 100×30像素
  - 位置: 右上角
  - 文字: "重置游戏"
  - 颜色: 橙色背景 (#FFA500)

#### 6.2.3 状态显示设计 (FR-014)
- **状态标签**:
  - 位置: 左上角
  - 文字: "状态: [当前状态]"
  - 字体: Arial, 14pt
  - 状态值: "游戏进行中" / "恭喜胜利!"

### 6.3 胜利界面设计 (FR-011)

```
┌─────────────────────────────────────────────────────┐
│                数字拼图游戏                           │
├─────────────────────────────────────────────────────┤
│  状态: 恭喜胜利!                     [重置游戏]      │
├─────────────────────────────────────────────────────┤
│                                                     │
│    ┌────┐ ┌────┐ ┌────┐ ┌────┐                     │
│    │ 1  │ │ 2  │ │ 3  │ │ 4  │                     │
│    └────┘ └────┘ └────┘ └────┘                     │
│                                                     │
│    ┌────┐ ┌────┐ ┌────┐ ┌────┐                     │
│    │ 5  │ │ 6  │ │ 7  │ │ 8  │                     │
│    └────┘ └────┘ └────┘ └────┘                     │
│                                                     │
│    ┌────┐ ┌────┐ ┌────┐ ┌────┐                     │
│    │ 9  │ │ 10 │ │ 11 │ │ 12 │                     │
│    └────┘ └────┘ └────┘ └────┘                     │
│                                                     │
│    ┌────┐ ┌────┐ ┌────┐ ┌────┐                     │
│    │ 13 │ │ 14 │ │ 15 │ │    │ ← 空格在右下角       │
│    └────┘ └────┘ └────┘ └────┘                     │
│                                                     │
│  ┌─────────────────────────────────────────────┐   │
│  │          🎉 恭喜您完成拼图! 🎉             │   │  ← FR-011: 胜利提示
│  └─────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────┘
```

## 7. 设计验证

### 7.1 需求覆盖验证

**功能需求覆盖**:
- ✅ FR-001: GameBoard类管理4×4网格数据
- ✅ FR-002: GameBoard存储数字1-15和空白格
- ✅ FR-003: PuzzleGameUI数字方块清晰显示
- ✅ FR-004: PuzzleGameUI空白格视觉区分
- ✅ FR-005: 时序图详细展示点击移动流程
- ✅ FR-006: GameBoard.isValidMove()验证相邻性
- ✅ FR-007: 对角线移动验证逻辑
- ✅ FR-008: 无效移动无反应处理
- ✅ FR-009: GameBoard.shuffleBoard()随机打乱
- ✅ FR-010: 状态图展示胜利判断逻辑
- ✅ FR-011: PuzzleGameUI.showWinMessage()胜利提示
- ✅ FR-012: PuzzleGame.resetGame()重新开始
- ✅ FR-013: 界面原型显示重置按钮位置
- ✅ FR-014: 界面原型包含状态显示
- ✅ FR-015: 界面设计简洁明了

**非功能需求覆盖**:
- ✅ NFR-001: 基于Java JDK 1.8设计
- ✅ NFR-002: PuzzleGameUI使用Swing框架
- ✅ NFR-003: 类图体现面向对象设计
- ✅ NFR-004: 系统架构实现MVC模式
- ✅ NFR-005-007: 时序图标注性能要求
- ✅ NFR-008-010: 界面设计考虑用户体验

### 7.2 架构验证

**MVC模式验证** (NFR-004):
- **Model**: GameBoard类专注数据管理
- **View**: PuzzleGameUI类负责界面显示和用户交互
- **Controller**: PuzzleGame类控制游戏逻辑和状态

**模块职责验证**:
- 数据模型模块: GameBoard类 ✅
- 游戏逻辑模块: PuzzleGame类 ✅  
- 用户界面模块: PuzzleGameUI类 ✅
- 主程序模块: Main类 ✅

## 8. 实现指导

### 8.1 开发优先级
1. **第一阶段**: 实现GameBoard核心数据模型
2. **第二阶段**: 实现PuzzleGame游戏逻辑
3. **第三阶段**: 实现PuzzleGameUI界面和交互
4. **第四阶段**: 集成测试和性能优化

### 8.2 关键技术点
- **随机打乱算法**: 确保生成可解的拼图配置
- **移动验证**: 精确的相邻性判断逻辑
- **胜利检测**: 高效的状态比较算法
- **界面响应**: 满足性能要求的事件处理

---

**文档版本**: v1.0  
**创建日期**: 2025-08-12  
**设计者**: AI Assistant  
**审核状态**: 待审核