import java.util.ArrayList;
import java.util.List;

/**
 * PuzzleGame类 - 数字拼图游戏的控制器层
 * 按照MVC模式作为Controller，负责游戏逻辑控制和状态管理
 * 
 * 对应需求：
 * NFR-004: MVC模式Controller层实现
 * FR-005: 处理用户移动请求
 * FR-008: 无效移动处理
 * FR-010: 胜利条件检查
 * FR-011: 胜利状态通知
 * FR-012: 重置游戏功能
 * NFR-006: 胜利判断响应时间不超过50毫秒
 */
public class PuzzleGame {
    private GameBoard gameBoard;                    // 游戏数据模型
    private GameState currentState;                 // 当前游戏状态
    private List<GameStateListener> listeners;      // 状态变化监听器列表
    
    /**
     * 构造函数 - 初始化游戏控制器
     */
    public PuzzleGame() {
        this.gameBoard = new GameBoard();
        this.currentState = GameState.PLAYING;
        this.listeners = new ArrayList<GameStateListener>();
    }
    
    /**
     * 尝试移动指定位置的方块 (FR-005)
     * @param row 要移动的方块行坐标
     * @param col 要移动的方块列坐标
     * @return 如果移动成功返回true，否则返回false
     */
    public boolean attemptMove(int row, int col) {
        // 只有在游戏进行中状态才能移动
        if (currentState != GameState.PLAYING) {
            return false;
        }
        
        // 验证移动是否有效 (FR-006, FR-007)
        if (!gameBoard.isValidMove(row, col)) {
            return false; // 无效移动，无反应 (FR-008)
        }
        
        // 执行移动
        gameBoard.moveBlock(row, col);
        
        // 检查胜利条件 (FR-010, NFR-006)
        checkWinCondition();
        
        // 通知状态变化
        notifyGameStateChanged();
        
        return true;
    }
    
    /**
     * 重置游戏 (FR-012)
     * 重新打乱数字方块，开始新游戏
     */
    public void resetGame() {
        // 设置重置状态
        currentState = GameState.RESET;
        notifyGameStateChanged();
        
        // 重新打乱游戏板 (FR-009)
        gameBoard.shuffleBoard();
        
        // 确保重置后不是胜利状态
        while (gameBoard.isWinState()) {
            gameBoard.shuffleBoard();
        }
        
        // 设置为游戏进行中状态
        currentState = GameState.PLAYING;
        notifyGameStateChanged();
    }
    
    /**
     * 获取当前游戏状态
     * @return 当前游戏状态
     */
    public GameState getGameState() {
        return currentState;
    }
    
    /**
     * 获取游戏板数据的副本
     * 为View层提供数据访问接口
     * @return 4×4数字网格的副本
     */
    public int[][] getBoard() {
        return gameBoard.getBoard();
    }
    
    /**
     * 获取游戏板大小
     * @return 游戏板大小（4）
     */
    public int getBoardSize() {
        return gameBoard.getSize();
    }
    
    /**
     * 获取指定位置的数值
     * @param row 行坐标
     * @param col 列坐标
     * @return 该位置的数值
     */
    public int getValue(int row, int col) {
        return gameBoard.getValue(row, col);
    }
    
    /**
     * 获取空白格位置
     * @return 空白格的坐标点
     */
    public java.awt.Point getEmptyPosition() {
        return gameBoard.getEmptyPosition();
    }
    
    /**
     * 添加游戏状态变化监听器
     * 支持观察者模式，允许View层监听状态变化
     * @param listener 状态变化监听器
     */
    public void addGameStateListener(GameStateListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }
    
    /**
     * 移除游戏状态变化监听器
     * @param listener 要移除的监听器
     */
    public void removeGameStateListener(GameStateListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 检查是否可以移动指定位置的方块
     * 为View层提供移动验证接口
     * @param row 行坐标
     * @param col 列坐标
     * @return 如果可以移动返回true，否则返回false
     */
    public boolean canMove(int row, int col) {
        return currentState == GameState.PLAYING && gameBoard.isValidMove(row, col);
    }
    
    /**
     * 检查游戏是否处于可操作状态
     * @return 如果游戏可操作返回true，否则返回false
     */
    public boolean isPlayable() {
        return currentState == GameState.PLAYING;
    }
    
    /**
     * 检查游戏是否已胜利
     * @return 如果已胜利返回true，否则返回false
     */
    public boolean isWon() {
        return currentState == GameState.WON;
    }
    
    /**
     * 获取游戏统计信息（可扩展功能）
     * @return 游戏统计信息字符串
     */
    public String getGameInfo() {
        StringBuilder info = new StringBuilder();
        info.append("游戏状态: ");
        switch (currentState) {
            case PLAYING:
                info.append("进行中");
                break;
            case WON:
                info.append("恭喜胜利!");
                break;
            case RESET:
                info.append("重置中...");
                break;
        }
        return info.toString();
    }
    
    /**
     * 通知所有监听器游戏状态发生变化
     * 实现观察者模式的通知机制
     */
    private void notifyGameStateChanged() {
        for (GameStateListener listener : listeners) {
            try {
                listener.onGameStateChanged(currentState);
            } catch (Exception e) {
                // 防止监听器异常影响游戏逻辑
                System.err.println("Error notifying game state listener: " + e.getMessage());
            }
        }
    }
    
    /**
     * 检查胜利条件 (FR-010)
     * 如果达到胜利状态，更新游戏状态为WON
     * 性能要求：响应时间不超过50毫秒 (NFR-006)
     */
    private void checkWinCondition() {
        if (gameBoard.isWinState()) {
            currentState = GameState.WON;
        }
    }
    
    /**
     * 强制设置游戏状态（用于测试或特殊情况）
     * @param state 要设置的游戏状态
     */
    protected void setGameState(GameState state) {
        if (state != null && state != currentState) {
            currentState = state;
            notifyGameStateChanged();
        }
    }
    
    /**
     * 获取当前游戏板的字符串表示（用于调试）
     * @return 游戏板的字符串表示
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("PuzzleGame State: ").append(currentState).append("\n");
        sb.append("Board:\n");
        
        int[][] board = gameBoard.getBoard();
        for (int row = 0; row < gameBoard.getSize(); row++) {
            for (int col = 0; col < gameBoard.getSize(); col++) {
                if (board[row][col] == 0) {
                    sb.append("   ");
                } else {
                    sb.append(String.format("%2d ", board[row][col]));
                }
            }
            sb.append("\n");
        }
        
        return sb.toString();
    }
}