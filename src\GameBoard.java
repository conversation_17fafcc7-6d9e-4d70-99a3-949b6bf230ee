import java.awt.Point;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

/**
 * GameBoard类 - 数字拼图游戏的数据模型
 * 负责管理4×4数字网格数据，实现方块移动和胜利判断逻辑
 * 
 * 对应需求：
 * FR-001: 4×4数字网格数据存储
 * FR-002: 包含数字1-15和一个空白格
 * FR-005, FR-006, FR-007: 方块移动逻辑
 * FR-009: 随机打乱功能
 * FR-010: 胜利状态判断
 */
public class GameBoard {
    private int[][] board;           // 4×4数字网格 (FR-001)
    private Point emptyPosition;     // 空白格位置
    private static final int SIZE = 4;  // 网格大小
    private static final int EMPTY = 0; // 空白格标记
    private Random random;           // 随机数生成器
    
    /**
     * 构造函数 - 初始化游戏板并随机打乱
     */
    public GameBoard() {
        this.board = new int[SIZE][SIZE];
        this.emptyPosition = new Point();
        this.random = new Random();
        initializeBoard();
        shuffleBoard();
    }
    
    /**
     * 获取游戏板的副本 (FR-001)
     * @return 4×4数字网格的副本
     */
    public int[][] getBoard() {
        int[][] copy = new int[SIZE][SIZE];
        for (int i = 0; i < SIZE; i++) {
            System.arraycopy(board[i], 0, copy[i], 0, SIZE);
        }
        return copy;
    }
    
    /**
     * 获取空白格位置
     * @return 空白格的坐标点
     */
    public Point getEmptyPosition() {
        return new Point(emptyPosition.x, emptyPosition.y);
    }
    
    /**
     * 验证指定位置的方块是否可以移动 (FR-006, FR-007)
     * @param row 行坐标
     * @param col 列坐标
     * @return 如果可以移动返回true，否则返回false
     */
    public boolean isValidMove(int row, int col) {
        // 检查坐标是否在有效范围内
        if (row < 0 || row >= SIZE || col < 0 || col >= SIZE) {
            return false;
        }
        
        // 检查是否是空白格本身
        if (isEmptyPosition(row, col)) {
            return false;
        }
        
        // 检查是否与空白格相邻 (FR-006)
        // 对角线相邻不能移动 (FR-007)
        return isAdjacent(row, col);
    }
    
    /**
     * 移动指定位置的方块到空白格 (FR-005)
     * @param row 要移动的方块行坐标
     * @param col 要移动的方块列坐标
     */
    public void moveBlock(int row, int col) {
        if (!isValidMove(row, col)) {
            return; // 无效移动，不执行任何操作 (FR-008)
        }
        
        swapWithEmpty(row, col);
    }
    
    /**
     * 随机打乱游戏板 (FR-009)
     */
    public void shuffleBoard() {
        // 重置为有序状态
        initializeBoard();
        
        // 通过有效移动来打乱，确保最终状态是可解的
        int shuffleMoves = 1000; // 执行1000次随机有效移动
        
        for (int i = 0; i < shuffleMoves; i++) {
            List<Point> validMoves = getValidMoves();
            if (!validMoves.isEmpty()) {
                Point randomMove = validMoves.get(random.nextInt(validMoves.size()));
                swapWithEmpty(randomMove.x, randomMove.y);
            }
        }
    }
    
    /**
     * 判断当前状态是否为胜利状态 (FR-010)
     * 胜利条件：数字1-15按顺序排列，空格在右下角
     * @return 如果达到胜利状态返回true，否则返回false
     */
    public boolean isWinState() {
        // 检查数字1-15是否按顺序排列
        int expectedValue = 1;
        for (int row = 0; row < SIZE; row++) {
            for (int col = 0; col < SIZE; col++) {
                if (row == SIZE - 1 && col == SIZE - 1) {
                    // 最后一个位置应该是空白格
                    if (board[row][col] != EMPTY) {
                        return false;
                    }
                } else {
                    if (board[row][col] != expectedValue) {
                        return false;
                    }
                    expectedValue++;
                }
            }
        }
        return true;
    }
    
    /**
     * 初始化游戏板为有序状态 (FR-002)
     * 数字1-15按顺序排列，空格在右下角
     */
    private void initializeBoard() {
        int value = 1;
        for (int row = 0; row < SIZE; row++) {
            for (int col = 0; col < SIZE; col++) {
                if (row == SIZE - 1 && col == SIZE - 1) {
                    // 右下角为空白格
                    board[row][col] = EMPTY;
                    emptyPosition.setLocation(row, col);
                } else {
                    board[row][col] = value++;
                }
            }
        }
    }
    
    /**
     * 检查指定位置是否与空白格相邻 (FR-006)
     * 只有水平或垂直相邻才返回true，对角线相邻返回false (FR-007)
     * @param row 行坐标
     * @param col 列坐标
     * @return 如果相邻返回true，否则返回false
     */
    private boolean isAdjacent(int row, int col) {
        int emptyRow = emptyPosition.x;
        int emptyCol = emptyPosition.y;
        
        // 计算行和列的距离
        int rowDiff = Math.abs(row - emptyRow);
        int colDiff = Math.abs(col - emptyCol);
        
        // 只有水平或垂直相邻（距离为1且另一个距离为0）才能移动
        return (rowDiff == 1 && colDiff == 0) || (rowDiff == 0 && colDiff == 1);
    }
    
    /**
     * 将指定位置的方块与空白格交换
     * @param row 要交换的方块行坐标
     * @param col 要交换的方块列坐标
     */
    private void swapWithEmpty(int row, int col) {
        int emptyRow = emptyPosition.x;
        int emptyCol = emptyPosition.y;
        
        // 交换数值
        board[emptyRow][emptyCol] = board[row][col];
        board[row][col] = EMPTY;
        
        // 更新空白格位置
        emptyPosition.setLocation(row, col);
    }
    
    /**
     * 检查指定位置是否为空白格
     * @param row 行坐标
     * @param col 列坐标
     * @return 如果是空白格返回true，否则返回false
     */
    private boolean isEmptyPosition(int row, int col) {
        return emptyPosition.x == row && emptyPosition.y == col;
    }
    
    /**
     * 获取当前所有可能的有效移动位置
     * @return 包含所有可移动位置的列表
     */
    private List<Point> getValidMoves() {
        List<Point> validMoves = new ArrayList<Point>();
        
        for (int row = 0; row < SIZE; row++) {
            for (int col = 0; col < SIZE; col++) {
                if (isValidMove(row, col)) {
                    validMoves.add(new Point(row, col));
                }
            }
        }
        
        return validMoves;
    }
    
    /**
     * 获取网格大小
     * @return 网格大小（4）
     */
    public int getSize() {
        return SIZE;
    }
    
    /**
     * 获取指定位置的数值
     * @param row 行坐标
     * @param col 列坐标
     * @return 该位置的数值，如果越界返回-1
     */
    public int getValue(int row, int col) {
        if (row < 0 || row >= SIZE || col < 0 || col >= SIZE) {
            return -1;
        }
        return board[row][col];
    }
    
    /**
     * 检查游戏板是否处于初始有序状态
     * @return 如果是有序状态返回true，否则返回false
     */
    public boolean isOrdered() {
        return isWinState();
    }
}