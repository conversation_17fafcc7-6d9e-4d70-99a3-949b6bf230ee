import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.UnsupportedLookAndFeelException;

/**
 * Main类 - 数字拼图游戏的程序入口点
 * 负责程序启动和模块协调
 * 
 * 对应需求：
 * NFR-001: 使用Java JDK 1.8开发
 * NFR-002: 使用Swing作为GUI框架
 * NFR-004: MVC模式的模块协调
 * 需求4.2节: 必须包含Main类作为程序入口点
 */
public class Main {
    
    /**
     * 程序主入口方法
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 设置系统外观和感觉 (NFR-002)
        setSystemLookAndFeel();
        
        // 使用Swing的事件分发线程来启动GUI
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                try {
                    // 初始化并启动游戏
                    initializeAndStartGame();
                } catch (Exception e) {
                    // 异常处理，确保程序不会崩溃
                    handleStartupError(e);
                }
            }
        });
    }
    
    /**
     * 初始化游戏并启动界面
     * 按照MVC模式初始化各个模块 (NFR-004)
     */
    private static void initializeAndStartGame() {
        System.out.println("正在启动数字拼图游戏...");
        
        // 1. 初始化Model层 - GameBoard在PuzzleGame构造函数中自动创建
        System.out.println("初始化游戏数据模型...");
        
        // 2. 初始化Controller层 - PuzzleGame
        System.out.println("初始化游戏控制器...");
        PuzzleGame puzzleGame = new PuzzleGame();
        
        // 3. 初始化View层 - PuzzleGameUI (需要在实现UI时取消注释)
        System.out.println("初始化用户界面...");
        // PuzzleGameUI gameUI = new PuzzleGameUI(puzzleGame);
        
        // 4. 显示界面
        // gameUI.show();
        
        System.out.println("数字拼图游戏启动完成!");
        System.out.println("游戏状态: " + puzzleGame.getGameInfo());
        
        // 临时输出游戏板状态（用于测试，UI完成后可删除）
        System.out.println("当前游戏板:");
        System.out.println(puzzleGame.toString());
        
        // 临时提示（UI完成后可删除）
        System.out.println("注意: UI界面尚未实现，请先实现PuzzleGameUI类");
    }
    
    /**
     * 设置系统外观和感觉
     * 使程序具有原生操作系统的外观 (NFR-008: 界面直观易懂)
     */
    private static void setSystemLookAndFeel() {
        try {
            // 尝试设置系统默认的外观和感觉
             UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            System.out.println("已设置系统外观主题");
        } catch (ClassNotFoundException e) {
            System.err.println("警告: 未找到系统外观类，使用默认外观");
        } catch (InstantiationException e) {
            System.err.println("警告: 无法实例化系统外观，使用默认外观");
        } catch (IllegalAccessException e) {
            System.err.println("警告: 无法访问系统外观，使用默认外观");
        } catch (UnsupportedLookAndFeelException e) {
            System.err.println("警告: 不支持的系统外观，使用默认外观");
        }
    }
    
    /**
     * 处理启动过程中的异常
     * @param e 启动异常
     */
    private static void handleStartupError(Exception e) {
        System.err.println("启动游戏时发生错误: " + e.getMessage());
        e.printStackTrace();
        
        // 显示错误对话框（可选）
        try {
            javax.swing.JOptionPane.showMessageDialog(
                null,
                "启动游戏时发生错误:\n" + e.getMessage() + 
                "\n\n请检查Java环境是否正确配置。",
                "数字拼图游戏 - 启动错误",
                javax.swing.JOptionPane.ERROR_MESSAGE
            );
        } catch (Exception dialogException) {
            // 如果连对话框都无法显示，则只能输出到控制台
            System.err.println("无法显示错误对话框: " + dialogException.getMessage());
        }
        
        // 退出程序
        System.exit(1);
    }
    
    /**
     * 获取应用程序信息
     * @return 应用程序信息字符串
     */
    public static String getApplicationInfo() {
        return "数字拼图游戏 v1.0\n" +
               "基于Java Swing的15-Puzzle游戏\n" +
               "使用MVC架构模式开发\n" +
               "兼容JDK 1.8+";
    }
    
    /**
     * 检查Java版本兼容性
     * 确保运行环境满足JDK 1.8要求 (NFR-001)
     */
    private static void checkJavaVersion() {
        String javaVersion = System.getProperty("java.version");
        System.out.println("当前Java版本: " + javaVersion);
        
        // 检查是否为JDK 1.8或更高版本
        if (javaVersion.startsWith("1.8") || 
            javaVersion.startsWith("9") || 
            javaVersion.startsWith("1.9") ||
            !javaVersion.startsWith("1.")) {
            System.out.println("Java版本检查通过");
        } else {
            System.err.println("警告: 当前Java版本可能不兼容，建议使用JDK 1.8或更高版本");
        }
    }
    
    /**
     * 打印启动横幅
     */
    private static void printStartupBanner() {
        System.out.println("================================");
        System.out.println("    数字拼图游戏 (15-Puzzle)    ");
        System.out.println("================================");
        System.out.println(getApplicationInfo());
        System.out.println("================================");
        checkJavaVersion();
        System.out.println("================================");
    }
    
    /**
     * 带启动横幅的主入口方法（可选使用）
     * @param args 命令行参数
     */
    public static void mainWithBanner(String[] args) {
        // 打印启动信息
        printStartupBanner();
        
        // 调用标准主方法
        main(args);
    }
}